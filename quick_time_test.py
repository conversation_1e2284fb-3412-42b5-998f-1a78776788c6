#!/usr/bin/env python3
"""
快速时间测试
"""

import random
from datetime import datetime, timedelta

def generate_random_time():
    """生成2025年全年的随机时间"""
    target_year = 2025
    # 2025年的开始和结束时间
    start_date = datetime(target_year, 1, 1)
    end_date = datetime(target_year, 12, 31, 23, 59, 59)
    
    # 计算总的时间范围（秒数）
    total_seconds = int((end_date - start_date).total_seconds())
    
    # 生成随机秒数
    random_seconds = random.randint(0, total_seconds)
    
    # 计算随机时间
    random_time = start_date + timedelta(seconds=random_seconds)
    return random_time

# 测试时间分布
print("🕒 测试2025年全年时间分布")
print("=" * 50)

month_counts = {}
for i in range(1, 13):
    month_counts[i] = 0

# 生成50个样本
print("生成的时间样本:")
for i in range(50):
    time = generate_random_time()
    month_counts[time.month] += 1
    if i < 10:
        print(f"  {time.strftime('%Y-%m-%d %H:%M:%S')}")

print(f"\n📊 月份分布 (50个样本):")
print("-" * 30)
for month in range(1, 13):
    count = month_counts[month]
    bar = "█" * count
    print(f"{month:>2}月: {count:>2} {bar}")

months_with_data = len([m for m in range(1, 13) if month_counts[m] > 0])
print(f"\n✅ 有数据的月份: {months_with_data}/12")
print("✅ 时间分布验证通过 - 数据已分布到全年" if months_with_data >= 8 else "⚠️ 需要更多样本验证")
