#!/usr/bin/env python3
"""
测试APP出卡和PC端出卡逻辑
"""

from generate_card_data import CardDataGenerator
import logging
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_app_pc_logic():
    """测试APP出卡和PC端出卡的逻辑"""
    
    logger.info("🧪 测试APP出卡和PC端出卡逻辑...")
    
    # 创建生成器实例
    generator = CardDataGenerator()
    
    # 统计数据
    app_orders = 0
    pc_orders = 0
    app_owner_ids = set()
    pc_company_ids = set()
    
    logger.info("生成100个订单样本进行测试...")
    
    for i in range(100):
        data = generator.generate_business_data()
        
        if data['is_app_order']:
            app_orders += 1
            app_owner_ids.add(data['ck_owner_id'])
            
            # 验证APP出卡的字段
            assert data['ck_owner_type'] == 'User', f"APP出卡owner_type应该是User，实际是{data['ck_owner_type']}"
            assert data['ck_company_id'] is None, f"APP出卡company_id应该是None，实际是{data['ck_company_id']}"
            assert 25000 <= data['ck_owner_id'] <= 27000, f"APP出卡owner_id应该在25000-27000范围，实际是{data['ck_owner_id']}"
            
            if i < 5:  # 显示前5个APP出卡样本
                logger.info(f"APP出卡样本 {app_orders}: owner_id={data['ck_owner_id']}, company_id={data['ck_company_id']}, owner_type={data['ck_owner_type']}")
        else:
            pc_orders += 1
            pc_company_ids.add(data['ck_company_id'])
            
            # 验证PC端出卡的字段
            assert data['ck_owner_type'] == 'AdminUser', f"PC端出卡owner_type应该是AdminUser，实际是{data['ck_owner_type']}"
            assert data['ck_company_id'] is not None, f"PC端出卡company_id不应该是None"
            
            if pc_orders <= 5:  # 显示前5个PC端出卡样本
                logger.info(f"PC端出卡样本 {pc_orders}: owner_id={data['ck_owner_id']}, company_id={data['ck_company_id']}, owner_type={data['ck_owner_type']}")
    
    # 输出统计结果
    logger.info("\n📊 出卡类型分布统计:")
    logger.info("=" * 50)
    logger.info(f"APP出卡数量: {app_orders} ({app_orders}%)")
    logger.info(f"PC端出卡数量: {pc_orders} ({pc_orders}%)")
    logger.info(f"APP用户ID范围: {min(app_owner_ids) if app_owner_ids else 'N/A'} - {max(app_owner_ids) if app_owner_ids else 'N/A'}")
    logger.info(f"PC端公司ID数量: {len(pc_company_ids)}")
    
    # 验证分布是否合理（应该接近50%/50%）
    logger.info("\n✅ 分布验证:")
    if 40 <= app_orders <= 60:
        logger.info("✅ APP出卡和PC端出卡分布正常 (接近50%/50%)")
    else:
        logger.info(f"⚠️  分布可能有偏差 - APP:{app_orders}%, PC:{pc_orders}%")
    
    if len(app_owner_ids) >= 10:
        logger.info("✅ APP用户ID分布正常 (有足够的随机性)")
    else:
        logger.info(f"⚠️  APP用户ID分布可能不够随机 (只有{len(app_owner_ids)}个不同ID)")
    
    return {
        'app_orders': app_orders,
        'pc_orders': pc_orders,
        'app_owner_ids': len(app_owner_ids),
        'pc_company_ids': len(pc_company_ids)
    }

def test_single_order_generation():
    """测试单个订单的完整数据生成"""
    
    logger.info("\n🔍 测试单个订单的完整数据结构...")
    
    generator = CardDataGenerator()
    
    # 生成一个APP出卡订单
    for i in range(10):
        data = generator.generate_business_data()
        if data['is_app_order']:
            logger.info("\n📱 APP出卡订单数据结构:")
            logger.info(f"  订单ID: {data['order_id']}")
            logger.info(f"  出卡类型: {'APP出卡' if data['is_app_order'] else 'PC端出卡'}")
            logger.info(f"  出卡商ID: {data['ck_company_id']} (应该是None)")
            logger.info(f"  出卡人类型: {data['ck_owner_type']} (应该是User)")
            logger.info(f"  出卡人ID: {data['ck_owner_id']} (应该在25000-27000)")
            logger.info(f"  收卡商ID: {data['sk_company_id']}")
            logger.info(f"  收卡人ID: {data['sk_owner_id']}")
            logger.info(f"  卡面值: {data['face_value']}")
            logger.info(f"  汇率: {data['exchange_rate']}")
            break
    
    # 生成一个PC端出卡订单
    for i in range(10):
        data = generator.generate_business_data()
        if not data['is_app_order']:
            logger.info("\n💻 PC端出卡订单数据结构:")
            logger.info(f"  订单ID: {data['order_id']}")
            logger.info(f"  出卡类型: {'APP出卡' if data['is_app_order'] else 'PC端出卡'}")
            logger.info(f"  出卡商ID: {data['ck_company_id']} (应该有值)")
            logger.info(f"  出卡人类型: {data['ck_owner_type']} (应该是AdminUser)")
            logger.info(f"  出卡人ID: {data['ck_owner_id']}")
            logger.info(f"  收卡商ID: {data['sk_company_id']}")
            logger.info(f"  收卡人ID: {data['sk_owner_id']}")
            logger.info(f"  卡面值: {data['face_value']}")
            logger.info(f"  汇率: {data['exchange_rate']}")
            break

if __name__ == "__main__":
    try:
        # 测试出卡类型分布
        stats = test_app_pc_logic()
        
        # 测试单个订单数据结构
        test_single_order_generation()
        
        logger.info("\n🎉 所有测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
