#!/usr/bin/env python3
"""
测试改进后的连接管理机制
"""

import logging
import time
from generate_card_data import CardDataGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_connection_stability():
    """测试连接稳定性"""
    logger.info("🔧 测试改进后的连接管理机制...")
    
    generator = CardDataGenerator()
    generator.target_year = 2025
    generator.total_records = 500  # 测试500条记录
    generator.thread_count = 10    # 使用10个线程
    
    logger.info(f"测试配置:")
    logger.info(f"  记录数量: {generator.total_records}")
    logger.info(f"  线程数量: {generator.thread_count}")
    logger.info(f"  预期连接池大小: {generator.thread_count * 3} (线程数 * 3)")
    
    try:
        start_time = time.time()
        generator.generate_data()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"✅ 连接稳定性测试完成!")
        logger.info(f"   耗时: {duration:.2f}秒")
        logger.info(f"   平均速度: {generator.total_records/duration:.2f}条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 连接稳定性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        generator.cleanup()

def test_high_concurrency():
    """测试高并发场景"""
    logger.info("🚀 测试高并发场景...")
    
    generator = CardDataGenerator()
    generator.target_year = 2025
    generator.total_records = 1000  # 测试1000条记录
    generator.thread_count = 15     # 使用15个线程
    
    logger.info(f"高并发测试配置:")
    logger.info(f"  记录数量: {generator.total_records}")
    logger.info(f"  线程数量: {generator.thread_count}")
    logger.info(f"  预期连接池大小: {generator.thread_count * 3} (线程数 * 3)")
    
    try:
        start_time = time.time()
        generator.generate_data()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"✅ 高并发测试完成!")
        logger.info(f"   耗时: {duration:.2f}秒")
        logger.info(f"   平均速度: {generator.total_records/duration:.2f}条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 高并发测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        generator.cleanup()

def test_connection_recovery():
    """测试连接恢复机制"""
    logger.info("🔄 测试连接恢复机制...")
    
    generator = CardDataGenerator()
    generator.target_year = 2025
    generator.total_records = 200   # 测试200条记录
    generator.thread_count = 8      # 使用8个线程
    
    logger.info(f"连接恢复测试配置:")
    logger.info(f"  记录数量: {generator.total_records}")
    logger.info(f"  线程数量: {generator.thread_count}")
    
    try:
        start_time = time.time()
        generator.generate_data()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"✅ 连接恢复测试完成!")
        logger.info(f"   耗时: {duration:.2f}秒")
        logger.info(f"   平均速度: {generator.total_records/duration:.2f}条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 连接恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        generator.cleanup()

if __name__ == "__main__":
    try:
        logger.info("开始测试改进后的连接管理机制...")
        
        # 测试1: 连接稳定性
        success1 = test_connection_stability()
        
        if success1:
            # 测试2: 高并发场景
            success2 = test_high_concurrency()
            
            if success2:
                # 测试3: 连接恢复机制
                success3 = test_connection_recovery()
                
                if success3:
                    logger.info("\n🎉 所有测试通过!")
                    logger.info("连接管理机制改进成功，主要改进包括:")
                    logger.info("  1. 增加连接池大小到线程数*3")
                    logger.info("  2. 添加连接池预热机制")
                    logger.info("  3. 改进连接获取重试机制（指数退避）")
                    logger.info("  4. 添加连接健康检查")
                    logger.info("  5. 添加连接监控和自动恢复")
                    logger.info("  6. 改进错误处理和连续错误控制")
                    logger.info("  7. 添加自动重启机制")
                else:
                    logger.error("❌ 连接恢复测试失败")
            else:
                logger.error("❌ 高并发测试失败")
        else:
            logger.error("❌ 连接稳定性测试失败")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
