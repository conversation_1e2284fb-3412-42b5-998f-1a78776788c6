#!/usr/bin/env python3
"""
测试连接池修复
验证连接池耗尽问题是否解决
"""

from generate_card_data import CardDataGenerator
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_connection_pool_fix():
    """测试连接池修复"""
    
    logger.info("🔧 测试连接池修复...")
    
    # 创建生成器实例
    generator = CardDataGenerator()
    generator.target_year = 2025
    generator.total_records = 100  # 先测试100条记录
    generator.thread_count = 10    # 使用10个线程
    
    logger.info(f"测试配置:")
    logger.info(f"  记录数量: {generator.total_records}")
    logger.info(f"  线程数量: {generator.thread_count}")
    logger.info(f"  预期连接池大小: {generator.thread_count * 2} (线程数 * 2)")
    
    try:
        # 执行数据生成
        start_time = time.time()
        generator.generate_data()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"✅ 连接池测试完成!")
        logger.info(f"   耗时: {duration:.2f}秒")
        logger.info(f"   平均速度: {generator.total_records/duration:.2f}条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 连接池测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_large_batch():
    """测试大批量数据生成"""
    
    logger.info("\n🚀 测试大批量数据生成...")
    
    # 创建生成器实例
    generator = CardDataGenerator()
    generator.target_year = 2025
    generator.total_records = 1000  # 测试1000条记录
    generator.thread_count = 10     # 使用10个线程
    
    logger.info(f"大批量测试配置:")
    logger.info(f"  记录数量: {generator.total_records}")
    logger.info(f"  线程数量: {generator.thread_count}")
    logger.info(f"  连接池大小: {generator.thread_count * 2}")
    
    try:
        # 执行数据生成
        start_time = time.time()
        generator.generate_data()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"✅ 大批量测试完成!")
        logger.info(f"   耗时: {duration:.2f}秒")
        logger.info(f"   平均速度: {generator.total_records/duration:.2f}条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 大批量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        # 测试连接池修复
        success1 = test_connection_pool_fix()
        
        if success1:
            # 如果小批量测试成功，进行大批量测试
            success2 = test_large_batch()
            
            if success2:
                logger.info("\n🎉 所有测试通过!")
                logger.info("连接池问题已修复，可以安全运行大批量数据生成")
                logger.info("\n💡 修复内容:")
                logger.info("  1. 增加连接池大小到线程数*2")
                logger.info("  2. 添加连接获取重试机制")
                logger.info("  3. 添加连接失效重连逻辑")
                logger.info("  4. 优化连接超时配置")
                logger.info("  5. 改进错误处理和日志记录")
            else:
                logger.error("❌ 大批量测试失败，需要进一步优化")
        else:
            logger.error("❌ 基础连接池测试失败")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
