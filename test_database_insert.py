#!/usr/bin/env python3
"""
测试数据库插入功能
验证APP出卡和PC端出卡的数据库记录是否正确
"""

from generate_card_data import CardDataGenerator
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_database_insert():
    """测试数据库插入功能"""
    
    logger.info("🗄️  测试数据库插入功能...")
    
    # 创建生成器实例
    generator = CardDataGenerator()
    generator.target_year = 2025
    generator.total_records = 10  # 只生成10条记录进行测试
    generator.thread_count = 2    # 使用2个线程
    
    logger.info(f"测试配置:")
    logger.info(f"  记录数量: {generator.total_records}")
    logger.info(f"  线程数量: {generator.thread_count}")
    logger.info(f"  目标年份: {generator.target_year}")
    
    try:
        # 执行数据生成
        start_time = time.time()
        generator.generate_data()
        end_time = time.time()
        
        duration = end_time - start_time
        logger.info(f"✅ 数据库插入测试完成!")
        logger.info(f"   耗时: {duration:.2f}秒")
        logger.info(f"   平均速度: {generator.total_records/duration:.2f}条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库插入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import time
    
    try:
        success = test_database_insert()
        
        if success:
            logger.info("\n🎉 数据库插入测试成功!")
            logger.info("现在可以检查数据库中的记录，验证:")
            logger.info("  1. orders表中有APP出卡记录 (company_id=NULL, owner_type='User')")
            logger.info("  2. orders表中有PC端出卡记录 (company_id有值, owner_type='AdminUser')")
            logger.info("  3. transactions表第一条记录的company_id和owner_type正确")
            logger.info("  4. 其他表的数据完整性")
        else:
            logger.error("❌ 数据库插入测试失败!")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
