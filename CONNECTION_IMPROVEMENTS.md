# MySQL 连接管理改进方案

## 问题描述

您遇到的 "MySQL Connection not available" 错误是一个常见的高并发数据库连接问题，主要原因包括：

1. **连接池耗尽**：多个线程同时请求连接，超过了连接池的容量
2. **连接泄漏**：连接没有正确释放回连接池
3. **连接超时**：长时间持有连接导致超时
4. **网络不稳定**：与数据库服务器的连接不稳定

## 改进方案

### 1. 连接池大小优化

**改进前：**
```python
pool_size = max(self.thread_count * 2, 20)  # 线程数*2
```

**改进后：**
```python
pool_size = max(self.thread_count * 3, 30)  # 线程数*3，最少30个连接
```

**优势：**
- 增加连接池容量，减少连接争用
- 为高并发场景提供更多缓冲

### 2. 连接池预热机制

**新增功能：**
```python
def _warmup_connection_pool(self):
    """预热连接池，创建初始连接"""
    warmup_connections = []
    warmup_count = min(5, self.thread_count)
    
    for _ in range(warmup_count):
        conn = self.connection_pool.get_connection()
        warmup_connections.append(conn)
    
    # 立即释放这些连接回池中
    for conn in warmup_connections:
        conn.close()
```

**优势：**
- 提前创建连接，减少运行时的连接创建延迟
- 验证连接池配置的正确性

### 3. 指数退避重试策略

**改进前：**
```python
wait_time = (attempt + 1) * 2  # 线性增长：2, 4, 6, 8秒
```

**改进后：**
```python
wait_time = min(2 ** attempt, 10)  # 指数增长，最大10秒
```

**优势：**
- 更智能的重试策略，避免雪崩效应
- 快速恢复 vs 避免过载的平衡

### 4. 连接健康检查

**新增功能：**
```python
def get_connection_with_retry(self, max_retries=10):
    connection = self.connection_pool.get_connection()
    # 测试连接是否有效
    if connection.is_connected():
        return connection
    else:
        # 连接无效，关闭并重试
        connection.close()
        continue
```

**优势：**
- 确保获取的连接是有效的
- 自动清理无效连接

### 5. 连接监控和自动恢复

**新增类：**
```python
class ConnectionMonitor:
    """连接池监控和自动恢复类"""
    
    def _check_pool_health(self):
        """检查连接池健康状态"""
        test_conn = self.connection_pool.get_connection()
        if test_conn.is_connected():
            test_conn.close()
        else:
            logger.warning("连接池健康检查失败")
```

**优势：**
- 主动监控连接池状态
- 及时发现和报告连接问题

### 6. 改进的错误处理

**新增功能：**
```python
consecutive_errors = 0  # 连续错误计数
max_consecutive_errors = 10  # 最大连续错误数

# 如果连续错误过多，暂停一下
if consecutive_errors >= max_consecutive_errors:
    logger.warning(f"线程 {thread_id}: 连续错误过多，暂停5秒")
    time.sleep(5)
    consecutive_errors = 0
```

**优势：**
- 防止错误雪崩
- 给系统恢复时间

### 7. 自动重启机制

**新增功能：**
```python
def run_with_auto_recovery():
    """带自动恢复的运行函数"""
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            generator.generate_data()
            break
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                wait_time = retry_count * 30
                time.sleep(wait_time)
```

**优势：**
- 程序级别的自动恢复
- 减少人工干预需求

## 配置优化

### 数据库连接配置

```python
self.db_config = {
    'connect_timeout': 60,  # 增加连接超时时间
    'pool_reset_session': True,  # 重置会话状态
    # 移除了不支持的 pool_timeout 参数
}
```

### 线程配置建议

- **小规模**（< 1000条）：5-8个线程
- **中等规模**（1000-10000条）：8-15个线程  
- **大规模**（> 10000条）：15-20个线程

## 使用方法

### 1. 直接运行改进后的代码

```bash
python generate_card_data.py
```

### 2. 使用自动恢复模式

代码已经默认使用 `run_with_auto_recovery()` 函数。

### 3. 监控运行状态

观察日志输出：
- 连接池大小信息
- 连接监控状态
- 重试和恢复信息
- 性能统计

## 预期效果

1. **大幅减少** "MySQL Connection not available" 错误
2. **提高稳定性**：自动处理连接问题
3. **提升性能**：更好的连接管理和重试策略
4. **增强可靠性**：自动监控和恢复机制

## 测试验证

运行测试脚本验证改进效果：

```bash
# 测试连接管理逻辑
python test_connection_logic.py

# 测试实际连接（需要数据库可用）
python test_improved_connection.py
```

## 注意事项

1. **数据库服务器限制**：确保数据库服务器支持足够的并发连接
2. **网络稳定性**：在网络不稳定的环境中，可能仍会遇到连接问题
3. **资源监控**：监控数据库服务器的CPU、内存和连接数使用情况
4. **日志分析**：定期分析日志，识别潜在问题

通过这些改进，您的程序应该能够更稳定地处理高并发数据生成任务，大大减少连接相关的错误。
